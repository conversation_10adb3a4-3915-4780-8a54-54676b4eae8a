{% extends 'base.html' %}
{% load static %}
{% load paypervisit_extras %}
{% load currency_filters %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/paypervisit.css' %}">
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">

    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6 space-y-2 sm:space-y-0">
            <div class="flex items-center">
                <a href="{% url 'paypervisit:index' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3 transition duration-200">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <h2 class="text-2xl font-bold text-gray-800">{% trans "Pay-per-visit Settings" %}</h2>
            </div>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-cog text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Price Configuration</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tag mr-1"></i>
                            {% trans "Price Per Person (KHR)" %}*
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <span class="text-gray-500 font-bold">៛</span>
                            </div>
                            <input class="border border-gray-300 w-full p-4 pl-10 text-xl font-bold leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200"
                                   id="price_per_person"
                                   name="price_per_person"
                                   type="number"
                                   min="1"
                                   step="100"
                                   placeholder="{% trans 'Enter price' %}"
                                   value="{{ settings.price_per_person }}"
                                   required />
                        </div>
                        <p class="text-sm text-gray-500 mt-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            {% trans "Base price per person for pay-per-visit access" %}
                        </p>
                        <p class="text-xs text-blue-600 mt-1">
                            {% trans "Current value:" %} {{ settings.price_per_person|format_khr }}
                        </p>
                    </div>

                    <!-- Price Preview with Editable Fields -->
                    <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="flex justify-between items-center mb-3">
                            <h4 class="text-lg font-semibold text-blue-900">Quick Selection Settings</h4>
                            <div class="text-sm text-blue-700">
                                <i class="fas fa-info-circle mr-1"></i> Customize both the number of people and prices
                            </div>
                        </div>

                        <div class="grid grid-cols-1 gap-4">
                            <!-- 1 Person (Read-only) -->
                            <div class="border rounded p-3 bg-white">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="font-semibold">Base Price</h5>
                                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Default Selection</span>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Number of People</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-gray-100 rounded cursor-not-allowed"
                                               type="number"
                                               value="1"
                                               readonly />
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Price (៛)</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-gray-100 rounded cursor-not-allowed"
                                               type="number"
                                               value="{{ settings.price_per_person }}"
                                               readonly />
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Base price per person (set above)</p>
                            </div>

                            <!-- Quick Selection 1 (Fully Editable) -->
                            <div class="border rounded p-3 bg-white">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="font-semibold">Quick Selection 1</h5>
                                    <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Editable</span>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Number of People</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="quick_select_1"
                                               name="quick_select_1"
                                               type="number"
                                               min="2"
                                               value="{{ settings.quick_select_1 }}"
                                               required />
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Price (៛)</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="custom_price_1"
                                               name="custom_price_1"
                                               type="number"
                                               min="1"
                                               value="{{ settings.custom_price_1 }}"
                                               required />
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Default price: {{ settings.price_per_person|multiply:settings.quick_select_1 }}៛</p>
                            </div>

                            <!-- Quick Selection 2 (Fully Editable) -->
                            <div class="border rounded p-3 bg-white">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="font-semibold">Quick Selection 2</h5>
                                    <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Editable</span>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Number of People</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="quick_select_2"
                                               name="quick_select_2"
                                               type="number"
                                               min="2"
                                               value="{{ settings.quick_select_2 }}"
                                               required />
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Price (៛)</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="custom_price_2"
                                               name="custom_price_2"
                                               type="number"
                                               min="1"
                                               value="{{ settings.custom_price_2 }}"
                                               required />
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Default price: {{ settings.price_per_person|multiply:settings.quick_select_2 }}៛</p>
                            </div>

                            <!-- Quick Selection 3 (Fully Editable) -->
                            <div class="border rounded p-3 bg-white">
                                <div class="flex justify-between items-center mb-2">
                                    <h5 class="font-semibold">Quick Selection 3</h5>
                                    <span class="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">Editable</span>
                                </div>

                                <div class="grid grid-cols-2 gap-3">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Number of People</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="quick_select_3"
                                               name="quick_select_3"
                                               type="number"
                                               min="2"
                                               value="{{ settings.quick_select_3 }}"
                                               required />
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Price (៛)</label>
                                        <input class="border w-full p-2 text-lg font-bold leading-tight bg-white rounded"
                                               id="custom_price_3"
                                               name="custom_price_3"
                                               type="number"
                                               min="1"
                                               value="{{ settings.custom_price_3 }}"
                                               required />
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Default price: {{ settings.price_per_person|multiply:settings.quick_select_3 }}៛</p>
                            </div>
                        </div>

                        <!-- Info Box -->
                        <div class="mt-3 p-3 bg-white rounded border border-blue-100">
                            <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> These settings control the quick selection buttons in the Pay-per-visit POS. You can customize both the number of people and the price for each selection.</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <a href="{% url 'paypervisit:index' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Notification System
        function showNotification(type, title, message, duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;

            let icon = 'info-circle';
            if (type === 'success') icon = 'check-circle';
            if (type === 'error') icon = 'exclamation-circle';
            if (type === 'warning') icon = 'exclamation-triangle';

            notification.innerHTML = `
                <i class="fas fa-${icon} notification-icon"></i>
                <div class="notification-content">
                    <div class="notification-title">${title}</div>
                    <div class="notification-message">${message}</div>
                </div>
                <button type="button" class="notification-close">&times;</button>
                <div class="notification-progress"></div>
            `;

            const notificationContainer = document.getElementById('notification-container');
            notificationContainer.appendChild(notification);

            // Close button functionality
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                closeNotification(notification);
            });

            // Auto close after duration
            setTimeout(() => {
                closeNotification(notification);
            }, duration);

            return notification;
        }

        function closeNotification(notification) {
            notification.classList.add('closing');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }

        // Check for Django messages and convert them to notifications
        {% if messages %}
            {% for message in messages %}
                {% if 'success' in message.tags %}
                    setTimeout(() => {
                        showNotification('success', 'Success', '{{ message }}', 7000);
                    }, 500);
                {% elif 'error' in message.tags %}
                    setTimeout(() => {
                        showNotification('error', 'Error', '{{ message }}', 7000);
                    }, 500);
                {% else %}
                    setTimeout(() => {
                        showNotification('info', 'Information', '{{ message }}', 7000);
                    }, 500);
                {% endif %}
            {% endfor %}
        {% endif %}
    });
</script>
{% endblock body %}
