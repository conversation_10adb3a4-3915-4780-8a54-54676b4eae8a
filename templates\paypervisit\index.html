{% extends 'base.html' %}

{% load static %}
{% load custom_filters %}
{% load currency_filters %}
{% load i18n %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/paypervisit.css' %}">
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">

    <div class="componentWrapper">
        <!-- POS-style Pay-per-visit Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <!-- Left Panel: Customer Input -->
            <div class="lg:col-span-2">
                <div class="bg-white p-4 rounded-lg shadow-md mb-4">
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-2 sm:space-y-0">
                        <h3 class="text-2xl font-bold text-gray-800">{% trans "Pay-per-visit POS" %}</h3>
                        <div class="flex flex-wrap gap-2 sm:space-x-3">
                            <a href="{% url 'paypervisit:transaction' %}" class="text-blue-900 hover:text-blue-700 hover:underline text-sm flex items-center">
                                <i class="fas fa-history mr-1"></i> {% trans "Transaction History" %}
                            </a>
                            <a href="{% url 'paypervisit:settings' %}" class="text-blue-900 hover:text-blue-700 hover:underline text-sm flex items-center">
                                <i class="fas fa-cog mr-1"></i> {% trans "Price Settings" %}
                            </a>
                        </div>
                    </div>

                    <!-- Quick Selection Buttons -->
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3 text-gray-700">{% trans "Quick Selection" %}</h4>
                        <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-2 sm:px-4 rounded-lg transition duration-200 text-sm sm:text-base" data-people="1">
                                <span class="block">1</span>
                                <span class="block text-xs">{% trans "Person" %}</span>
                            </button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-2 sm:px-4 rounded-lg transition duration-200 text-sm sm:text-base" data-people="{{ settings.quick_select_1 }}">
                                <span class="block">{{ settings.quick_select_1 }}</span>
                                <span class="block text-xs">{% trans "People" %}</span>
                            </button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-2 sm:px-4 rounded-lg transition duration-200 text-sm sm:text-base" data-people="{{ settings.quick_select_2 }}">
                                <span class="block">{{ settings.quick_select_2 }}</span>
                                <span class="block text-xs">{% trans "People" %}</span>
                            </button>
                            <button type="button" class="people-btn bg-blue-100 hover:bg-blue-200 text-blue-900 font-bold py-4 px-2 sm:px-4 rounded-lg transition duration-200 text-sm sm:text-base" data-people="{{ settings.quick_select_3 }}">
                                <span class="block">{{ settings.quick_select_3 }}</span>
                                <span class="block text-xs">{% trans "People" %}</span>
                            </button>
                        </div>
                    </div>

                    <!-- Custom Number Input with Numpad -->
                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3 text-gray-700">{% trans "Custom Number" %}</h4>
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                            <!-- Number Display -->
                            <div class="lg:col-span-1">
                                <label class="block text-sm font-medium text-gray-600 mb-2">{% trans "Number of Visitors" %}</label>
                                <div class="relative">
                                    <input class="border w-full p-4 text-2xl font-bold leading-tight bg-slate-100 pr-12 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           id="num_people_display"
                                           type="number"
                                           min="1"
                                           placeholder="{% trans 'Number of People' %}"
                                           value="1" />
                                    <div class="absolute inset-y-0 right-0 flex flex-col">
                                        <button type="button" id="increment-btn" class="h-1/2 px-3 bg-gray-200 hover:bg-gray-300 border-l rounded-tr-lg transition duration-200"><i class="fas fa-chevron-up"></i></button>
                                        <button type="button" id="decrement-btn" class="h-1/2 px-3 bg-gray-200 hover:bg-gray-300 border-l border-t rounded-br-lg transition duration-200"><i class="fas fa-chevron-down"></i></button>
                                    </div>
                                </div>
                                <p class="text-sm text-gray-500 mt-2">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    {% trans "Base rate:" %} {{ price_per_person|format_khr }} {% trans "per person" %}
                                </p>
                            </div>

                            <!-- Numpad -->
                            <div class="lg:col-span-2">
                                <label class="block text-sm font-medium text-gray-600 mb-2">{% trans "Number Pad" %}</label>
                                <div class="grid grid-cols-3 gap-2">
                                    <!-- Numbers 1-9 -->
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="1">1</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="2">2</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="3">3</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="4">4</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="5">5</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="6">6</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="7">7</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="8">8</button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="9">9</button>
                                    <!-- Last row -->
                                    <button type="button" id="numpad-clear" class="bg-red-100 hover:bg-red-200 border border-red-300 text-red-800 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" title="{% trans 'Clear' %}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button" class="numpad-btn bg-white hover:bg-gray-100 border border-gray-300 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" data-value="0">0</button>
                                    <button type="button" id="numpad-enter" class="bg-green-100 hover:bg-green-200 border border-green-300 text-green-800 text-xl font-bold py-3 rounded-lg transition duration-200 touch-manipulation" title="{% trans 'Process Payment' %}">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Summary -->
                    <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                        <h4 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                            <i class="fas fa-calculator mr-2"></i>
                            {% trans "Payment Summary" %}
                        </h4>

                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                            <!-- Visitors -->
                            <div class="bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                                <p class="text-sm text-gray-600 mb-2 font-medium">{% trans "Number of Visitors" %}</p>
                                <div class="flex items-center">
                                    <i class="fas fa-users text-xl text-blue-700 mr-3"></i>
                                    <span id="people-display" class="text-xl font-bold text-gray-800">1 {% trans "person" %}</span>
                                </div>
                            </div>

                            <!-- Rate -->
                            <div class="bg-white p-4 rounded-lg border border-blue-100 shadow-sm">
                                <p class="text-sm text-gray-600 mb-2 font-medium">{% trans "Rate per Person" %}</p>
                                <div class="flex items-center">
                                    <i class="fas fa-tag text-xl text-blue-700 mr-3"></i>
                                    <span class="text-xl font-bold text-gray-800">{{ price_per_person|format_khr }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Total -->
                        <div class="p-4 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg text-white shadow-md">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold flex items-center">
                                    <i class="fas fa-money-bill-wave mr-2"></i>
                                    {% trans "Total Amount:" %}
                                </span>
                                <span id="total-display" class="text-2xl font-bold">{{ price_per_person|format_khr }}</span>
                            </div>
                            <p class="text-xs text-blue-100 mt-2 opacity-90">
                                <i class="fas fa-info-circle mr-1"></i>
                                {% trans "This is the total amount to be paid for all visitors" %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Payment Summary and Checkout -->
            <div class="lg:col-span-1">
                <div class="bg-white p-4 rounded-lg shadow-md sticky top-4">
                    <div class="mb-4">
                        <h3 class="text-xl font-bold mb-2 text-gray-800 flex items-center">
                            <i class="fas fa-credit-card mr-2 text-blue-600"></i>
                            {% trans "Payment Details" %}
                        </h3>
                        <p class="text-gray-600 text-sm">{% trans "Enter the number of visitors and process payment" %}</p>
                    </div>

                    <form method="post" id="payment-form">
                        {% csrf_token %}
                        <!-- Hidden inputs for form submission -->
                        <input type="hidden" id="num_people" name="num_people" value="1" />
                        <input type="hidden" id="amount" name="amount" value="{{ price_per_person }}" />

                        <!-- Payment Method Selection -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-wallet mr-1"></i>
                                {% trans "Payment Method" %}*
                            </label>
                            <select name="payment_method" class="border border-gray-300 w-full p-3 leading-tight bg-slate-100 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200" required>
                                <option value="cash">{% trans "Cash" %}</option>
                                <option value="bank">{% trans "Bank Transfer" %}</option>
                                <option value="other">{% trans "Other" %}</option>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-info-circle mr-1"></i>
                                {% trans "Select the payment method used by the customer" %}
                            </p>
                        </div>

                        <!-- Receipt Preview -->
                        <div class="receipt-preview mb-6" id="receipt-preview">
                            <div class="receipt-paper">
                                <div class="receipt-header">
                                    <div class="receipt-logo">{% trans "Legend Fitness Club" %}</div>
                                    <h3 class="receipt-title">{% trans "Pay-per-visit Receipt" %}</h3>
                                    <p class="receipt-subtitle">{% trans "Kompongkrobey, Svaypoa, Battambang, Cambodia" %}</p>
                                    <p class="receipt-subtitle">{% trans "Tel: 070 201 530" %}</p>
                                    <p class="receipt-subtitle" id="receipt-date">{% now "F j, Y H:i" %}</p>
                                    <p class="receipt-subtitle" id="receipt-id">{% trans "Transaction ID:" %} <span id="receipt-trx-id">-</span></p>
                                </div>

                                <div class="receipt-divider"></div>
                                <div class="receipt-row">
                                    <span>{% trans "Cashier:" %}</span>
                                    <span id="receipt-cashier">{{ request.user.username }}</span>
                                </div>

                                <div class="receipt-divider"></div>

                                <div class="receipt-row">
                                    <span>{% trans "Payment Method:" %}</span>
                                    <span id="receipt-payment-method">{% trans "Cash" %}</span>
                                </div>

                                <div class="receipt-row">
                                    <span>{% trans "Visitors:" %}</span>
                                    <span id="receipt-people">1 {% trans "person" %}</span>
                                </div>
                                <div class="receipt-row">
                                    <span>{% trans "Rate:" %}</span>
                                    <span>{{ price_per_person|format_khr }} {% trans "per person" %}</span>
                                </div>

                                <div class="receipt-divider"></div>

                                <div class="receipt-total">
                                    <span>{% trans "TOTAL:" %}</span>
                                    <span id="receipt-total">{{ price_per_person|format_khr }}</span>
                                </div>

                                <div class="receipt-footer">
                                    <p>{% trans "Thank you for visiting Legend Fitness Club!" %}</p>
                                    <p>{% trans "Telegram: @LegendFitness" %}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="grid grid-cols-2 gap-3 mb-6">
                            <button type="button" id="clear-btn" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-3 rounded-lg flex items-center justify-center transition duration-200 touch-manipulation">
                                <i class="fas fa-times mr-2"></i> {% trans "Clear" %}
                            </button>
                            <button type="submit" id="process-btn" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-3 rounded-lg flex items-center justify-center transition duration-200 touch-manipulation shadow-md">
                                <i class="fas fa-check-circle mr-2"></i> {% trans "Process" %}
                            </button>
                        </div>

                        <!-- Keyboard Shortcuts Help -->
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-3 rounded-lg border border-blue-200">
                            <p class="text-xs text-blue-800 mb-2 font-semibold">
                                <i class="fas fa-keyboard mr-1"></i> {% trans "Keyboard Shortcuts:" %}
                            </p>
                            <div class="grid grid-cols-2 gap-2 text-xs text-blue-700">
                                <div class="flex items-center">
                                    <kbd class="bg-white text-blue-700 border border-blue-300 px-2 py-1 rounded shadow-sm mr-1">+</kbd>
                                    <span>{% trans "Increase" %}</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="bg-white text-blue-700 border border-blue-300 px-2 py-1 rounded shadow-sm mr-1">-</kbd>
                                    <span>{% trans "Decrease" %}</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="bg-white text-blue-700 border border-blue-300 px-2 py-1 rounded shadow-sm mr-1">Enter</kbd>
                                    <span>{% trans "Process" %}</span>
                                </div>
                                <div class="flex items-center">
                                    <kbd class="bg-white text-blue-700 border border-blue-300 px-2 py-1 rounded shadow-sm mr-1">Esc</kbd>
                                    <span>{% trans "Clear" %}</span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Form elements
                const paymentForm = document.getElementById('payment-form');
                const numPeopleDisplay = document.getElementById('num_people_display');
                const numPeopleInput = document.getElementById('num_people');
                const amountInput = document.getElementById('amount');

                // Display elements
                const peopleDisplay = document.getElementById('people-display');
                const totalDisplay = document.getElementById('total-display');

                // Receipt elements
                const receiptPreview = document.getElementById('receipt-preview');
                const receiptPeople = document.getElementById('receipt-people');
                const receiptTotal = document.getElementById('receipt-total');
                const receiptDate = document.getElementById('receipt-date');
                const receiptTrxId = document.getElementById('receipt-trx-id');
                const receiptPaymentMethod = document.getElementById('receipt-payment-method');

                // Buttons
                const incrementBtn = document.getElementById('increment-btn');
                const decrementBtn = document.getElementById('decrement-btn');
                const clearBtn = document.getElementById('clear-btn');
                const printBtn = document.getElementById('print-btn');
                const processBtn = document.getElementById('process-btn');
                const numpadClearBtn = document.getElementById('numpad-clear');
                const numpadEnterBtn = document.getElementById('numpad-enter');

                // Notification container
                const notificationContainer = document.getElementById('notification-container');

                // Notification System
                function showNotification(type, title, message, duration = 5000) {
                    const notification = document.createElement('div');
                    notification.className = `notification ${type}`;

                    let icon = 'info-circle';
                    if (type === 'success') icon = 'check-circle';
                    if (type === 'error') icon = 'exclamation-circle';
                    if (type === 'warning') icon = 'exclamation-triangle';

                    notification.innerHTML = `
                        <i class="fas fa-${icon} notification-icon"></i>
                        <div class="notification-content">
                            <div class="notification-title">${title}</div>
                            <div class="notification-message">${message}</div>
                        </div>
                        <button type="button" class="notification-close">&times;</button>
                        <div class="notification-progress"></div>
                    `;

                    notificationContainer.appendChild(notification);

                    // Close button functionality
                    const closeBtn = notification.querySelector('.notification-close');
                    closeBtn.addEventListener('click', () => {
                        closeNotification(notification);
                    });

                    // Auto close after duration
                    setTimeout(() => {
                        closeNotification(notification);
                    }, duration);

                    return notification;
                }

                function closeNotification(notification) {
                    notification.classList.add('closing');
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }

                // Constants
                const ratePerPerson = {{ price_per_person }};

                // Quick selection people counts
                const quickSelect1 = {{ settings.quick_select_1 }};
                const quickSelect2 = {{ settings.quick_select_2 }};
                const quickSelect3 = {{ settings.quick_select_3 }};

                // Custom prices
                const customPrice1 = {{ settings.custom_price_1 }};
                const customPrice2 = {{ settings.custom_price_2 }};
                const customPrice3 = {{ settings.custom_price_3 }};

                // Backward compatibility
                const priceFor2 = {{ settings.price_for_2 }};
                const priceFor5 = {{ settings.price_for_5 }};
                const priceFor10 = {{ settings.price_for_10 }};

                // Variables
                let currentInput = '';

                // Currency formatting function
                function formatKHR(value) {
                    if (value === null || value === undefined) return '0៛';
                    try {
                        const numValue = parseInt(value);
                        return numValue.toLocaleString('en-US') + '៛';
                    } catch (e) {
                        return '0៛';
                    }
                }

                // Function to update all displays
                function updateDisplays(numPeople) {
                    // Ensure numPeople is at least 1
                    numPeople = Math.max(1, numPeople);

                    // Update form inputs
                    numPeopleDisplay.value = numPeople;
                    numPeopleInput.value = numPeople;

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (numPeople === 1) {
                        totalAmount = ratePerPerson;
                    } else if (numPeople === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (numPeople === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (numPeople === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = numPeople * ratePerPerson;
                    }
                    amountInput.value = totalAmount;

                    // Update visual displays with proper translation
                    const personText = numPeople === 1 ? '{% trans "person" %}' : '{% trans "people" %}';
                    peopleDisplay.textContent = `${numPeople} ${personText}`;
                    totalDisplay.textContent = formatKHR(totalAmount);

                    // Update receipt preview
                    receiptPeople.textContent = `${numPeople} ${personText}`;
                    receiptTotal.textContent = formatKHR(totalAmount);

                    // Update receipt date
                    receiptDate.textContent = new Date().toLocaleString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    // Update active button
                    updateActiveButton(numPeople);
                }

                // Function to handle numpad input
                function handleNumpadInput(value) {
                    if (currentInput === '0') {
                        currentInput = value;
                    } else {
                        currentInput += value;
                    }

                    const numPeople = parseInt(currentInput) || 1;
                    updateDisplays(numPeople);

                    // Show notification for numpad input
                    const visitorText = numPeople === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (numPeople === 1) {
                        totalAmount = ratePerPerson;
                    } else if (numPeople === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (numPeople === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (numPeople === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = numPeople * ratePerPerson;
                    }

                    showNotification('info', `${numPeople} ${visitorText}`, `{% trans "Total amount:" %} ${formatKHR(totalAmount)}`, 2000);
                }

                // Function to clear numpad input
                function clearNumpadInput() {
                    currentInput = '';
                    updateDisplays(1);
                    showNotification('info', '{% trans "Reset to 1 Visitor" %}', '{% trans "Number of visitors has been reset to 1" %}', 3000);
                }

                // Function to print receipt
                function printReceipt() {
                    // Show notification
                    showNotification('info', '{% trans "Print Receipt" %}', '{% trans "Opening receipt preview..." %}', 3000);

                    // Generate a random transaction ID for preview if not already set
                    if (!receiptTrxId.textContent || receiptTrxId.textContent === 'LFC-PPV-XXXX') {
                        const randomId = 'LFC-PPV-' + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
                        receiptTrxId.textContent = randomId;
                    }

                    // Create a form to submit the receipt data
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '{% url "paypervisit:print_receipt" 0 %}';
                    form.target = '_blank';

                    // Add CSRF token
                    const csrfToken = document.createElement('input');
                    csrfToken.type = 'hidden';
                    csrfToken.name = 'csrfmiddlewaretoken';
                    csrfToken.value = '{{ csrf_token }}';
                    form.appendChild(csrfToken);

                    // Add receipt data
                    const addField = (name, value) => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = name;
                        input.value = value;
                        form.appendChild(input);
                    };

                    addField('trxId', receiptTrxId.textContent);
                    addField('num_people', numPeopleInput.value);
                    addField('amount', amountInput.value);
                    addField('date', new Date().toISOString());

                    // Add the form to the document and submit it
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form);
                }

                // Function to show processing animation (for UI feedback only)
                function showProcessingAnimation() {
                    // Show notification
                    const visitorText = numPeopleInput.value == 1 ? '{% trans "visitor" %}' : '{% trans "visitors" %}';
                    showNotification('info', '{% trans "Processing Payment" %}', `{% trans "Processing payment for" %} ${numPeopleInput.value} ${visitorText}...`, 2000);

                    // Disable the process button and show loading state
                    processBtn.disabled = true;
                    processBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> {% trans "Processing..." %}';

                    // Add success animation to receipt
                    receiptPreview.classList.add('success-animation');

                    // Remove animation class after it completes
                    setTimeout(() => {
                        receiptPreview.classList.remove('success-animation');
                    }, 500);
                }

                // Function to reset the process button after form submission
                function resetProcessButton() {
                    processBtn.disabled = false;
                    processBtn.innerHTML = '<i class="fas fa-check-circle mr-2"></i> {% trans "Process" %}';
                }

                // Function to handle form submission
                function submitPaymentForm() {
                    // Validate form data
                    const numPeople = parseInt(numPeopleInput.value);
                    const amount = parseInt(amountInput.value);
                    const paymentMethod = paymentMethodSelect.value;

                    if (!numPeople || numPeople < 1) {
                        showNotification('error', '{% trans "Invalid Input" %}', '{% trans "Number of people must be at least 1" %}', 5000);
                        return false;
                    }

                    if (!amount || amount < 1) {
                        showNotification('error', '{% trans "Invalid Input" %}', '{% trans "Amount must be greater than 0" %}', 5000);
                        return false;
                    }

                    if (!paymentMethod) {
                        showNotification('error', '{% trans "Invalid Input" %}', '{% trans "Please select a payment method" %}', 5000);
                        return false;
                    }

                    // Show processing animation
                    showProcessingAnimation();

                    // Submit the form
                    paymentForm.submit();

                    return true;
                }

                // Initialize with default value
                updateDisplays(1);

                // Function to update active button state
                function updateActiveButton(numPeople) {
                    // Remove active class from all buttons
                    document.querySelectorAll('.people-btn').forEach(btn => {
                        btn.classList.remove('bg-blue-500', 'text-white');
                        btn.classList.add('bg-blue-100', 'text-blue-900');
                    });

                    // Add active class to the matching button
                    const activeButton = document.querySelector(`.people-btn[data-people="${numPeople}"]`);
                    if (activeButton) {
                        activeButton.classList.remove('bg-blue-100', 'text-blue-900');
                        activeButton.classList.add('bg-blue-500', 'text-white');
                    }
                }

                // Event listeners for quick selection buttons
                document.querySelectorAll('.people-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const people = parseInt(this.getAttribute('data-people'));
                        currentInput = people.toString();
                        updateDisplays(people);
                        updateActiveButton(people);

                        // Show notification
                        const visitorText = people === 1 ? 'visitor' : 'visitors';

                        // Calculate total amount based on number of people
                        let totalAmount;
                        if (people === 1) {
                            totalAmount = ratePerPerson;
                        } else if (people === quickSelect1) {
                            totalAmount = customPrice1;
                        } else if (people === quickSelect2) {
                            totalAmount = customPrice2;
                        } else if (people === quickSelect3) {
                            totalAmount = customPrice3;
                        } else {
                            // For other numbers, use the base price
                            totalAmount = people * ratePerPerson;
                        }

                        showNotification('info', `${people} ${visitorText} selected`, `Total amount: ${totalAmount}៛`, 2000);
                    });
                });

                // Event listeners for numpad buttons
                document.querySelectorAll('.numpad-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const value = this.getAttribute('data-value');
                        handleNumpadInput(value);
                    });
                });

                // Event listener for numpad clear button
                numpadClearBtn.addEventListener('click', clearNumpadInput);

                // Event listener for numpad enter button
                numpadEnterBtn.addEventListener('click', function() {
                    submitPaymentForm();
                });

                // Event listener for custom number input
                numPeopleDisplay.addEventListener('input', function() {
                    const numPeople = parseInt(this.value) || 1;
                    currentInput = numPeople.toString();
                    updateDisplays(numPeople);
                });

                // Event listeners for increment/decrement buttons
                incrementBtn.addEventListener('click', function() {
                    const currentValue = parseInt(numPeopleDisplay.value) || 0;
                    const newValue = currentValue + 1;
                    currentInput = newValue.toString();
                    updateDisplays(newValue);
                    // Show notification for increment
                    const visitorText = newValue === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (newValue === 1) {
                        totalAmount = ratePerPerson;
                    } else if (newValue === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (newValue === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (newValue === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = newValue * ratePerPerson;
                    }

                    showNotification('info', `${newValue} ${visitorText}`, `Total amount: ${totalAmount}៛`, 2000);
                });

                decrementBtn.addEventListener('click', function() {
                    const currentValue = parseInt(numPeopleDisplay.value) || 2;
                    const newValue = Math.max(1, currentValue - 1);
                    currentInput = newValue.toString();
                    updateDisplays(newValue);
                    // Show notification for decrement
                    const visitorText = newValue === 1 ? 'visitor' : 'visitors';

                    // Calculate total amount based on number of people
                    let totalAmount;
                    if (newValue === 1) {
                        totalAmount = ratePerPerson;
                    } else if (newValue === quickSelect1) {
                        totalAmount = customPrice1;
                    } else if (newValue === quickSelect2) {
                        totalAmount = customPrice2;
                    } else if (newValue === quickSelect3) {
                        totalAmount = customPrice3;
                    } else {
                        // For other numbers, use the base price
                        totalAmount = newValue * ratePerPerson;
                    }

                    showNotification('info', `${newValue} ${visitorText}`, `Total amount: ${totalAmount}៛`, 2000);
                });

                // Event listener for clear button
                clearBtn.addEventListener('click', clearNumpadInput);

                // Event listener for print button
                printBtn.addEventListener('click', printReceipt);

                // Event listener for payment method change
                const paymentMethodSelect = document.querySelector('select[name="payment_method"]');
                paymentMethodSelect.addEventListener('change', function() {
                    // Update receipt payment method display
                    const selectedOption = this.options[this.selectedIndex];
                    receiptPaymentMethod.textContent = selectedOption.text;
                });

                // Event listener for form submission
                paymentForm.addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    submitPaymentForm(); // Use our custom submission function
                });

                // Keyboard shortcuts
                document.addEventListener('keydown', function(e) {
                    // Only process keyboard shortcuts if the focus is not in an input field
                    if (e.target.tagName.toLowerCase() === 'input') {
                        return;
                    }

                    // Number keys (0-9)
                    if (e.key >= '0' && e.key <= '9') {
                        handleNumpadInput(e.key);
                    }

                    // Plus key to increment
                    if (e.key === '+' || e.key === '=') {
                        incrementBtn.click();
                    }

                    // Minus key to decrement
                    if (e.key === '-' || e.key === '_') {
                        decrementBtn.click();
                    }

                    // Enter key to process payment
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        submitPaymentForm();
                    }

                    // Escape key to clear
                    if (e.key === 'Escape') {
                        clearNumpadInput();
                    }

                    // P key to print
                    if (e.key === 'p' || e.key === 'P') {
                        printReceipt();
                    }
                });

                // Check for Django messages and convert them to notifications
                {% if messages %}
                    {% for message in messages %}
                        {% if 'success' in message.tags %}
                            setTimeout(() => {
                                showNotification('success', 'Payment Complete', '{{ message }}', 7000);
                                // Reset form after successful payment
                                resetProcessButton();
                                clearNumpadInput();
                                // Generate new transaction ID for receipt
                                const newTrxId = 'LFC-PPV-' + new Date().toISOString().slice(0,10).replace(/-/g,'') + '-' + Math.random().toString(36).substr(2, 8);
                                receiptTrxId.textContent = newTrxId;
                            }, 500);
                        {% elif 'error' in message.tags %}
                            setTimeout(() => {
                                showNotification('error', 'Error', '{{ message }}', 7000);
                                resetProcessButton();
                            }, 500);
                        {% else %}
                            setTimeout(() => {
                                showNotification('info', 'Information', '{{ message }}', 7000);
                                resetProcessButton();
                            }, 500);
                        {% endif %}
                    {% endfor %}
                {% else %}
                    // Show welcome notification if no messages
                    setTimeout(() => {
                        showNotification('info', '{% trans "Pay-per-visit System" %}', '{% trans "Select the number of visitors using the quick buttons or numpad, then process payment" %}', 5000);
                    }, 1000);
                {% endif %}
            });
        </script>
    </div>
</div>
{% endblock %}
