/* Product POS Styles */

/* POS Page Container - Optimized Spacing */
.pos-page-container {
    padding: 0.75rem;
    background-color: #f9fafb;
    min-height: calc(100vh - 140px); /* Optimized height calculation */
}

/* Main Layout */
.pos-container {
    min-height: calc(100vh - 180px); /* Reduced height for better spacing */
    background-color: transparent;
    position: relative;
    overflow-x: hidden;
}

/* POS Layout Components */
.pos-header {
    padding: 0.75rem 1rem; /* Reduced vertical padding */
    background-color: white;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 0.75rem; /* Reduced margin */
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pos-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.pos-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
}

.pos-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem; /* Reduced gap for tighter layout */
}

@media (min-width: 768px) {
    .pos-content {
        grid-template-columns: 2fr 1fr;
    }
}

.pos-products-panel {
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.pos-cart-panel {
    position: relative;
}

@media (min-width: 768px) {
    .pos-cart-panel {
        position: sticky;
        top: 1rem;
    }
}

/* Product Grid Styles */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
    gap: 0.75rem;
    padding: 0.75rem; /* Reduced padding */
    overflow-y: auto;
    max-height: calc(100vh - 280px); /* Optimized height calculation */
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
}

.product-grid::-webkit-scrollbar {
    width: 6px;
}

.product-grid::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.product-grid::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
}

/* Product Item */
.product-item {
    position: relative;
    border-radius: 0.75rem;
    overflow: hidden;
    transition: all 0.25s ease;
    border: 1px solid #e5e7eb;
    background-color: white;
    padding: 0.75rem 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    height: 100%;
}

.product-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    border-color: #93c5fd;
    z-index: 1;
}

.product-item:active {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-item.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
    box-shadow: 0 0 0 2px #3b82f6, 0 6px 12px rgba(59, 130, 246, 0.1);
}

/* Product Badge */
.product-badge {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    background-color: #10b981;
    color: white;
    font-size: 0.6rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 1rem;
    z-index: 1;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.product-badge.new {
    background-color: #3b82f6;
}

.product-badge.sale {
    background-color: #ef4444;
}

/* Product Image */
.product-image-container {
    position: relative;
    width: 60px;
    height: 60px;
    margin-bottom: 0.75rem;
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-item:hover .product-image {
    transform: scale(1.08);
}

/* Product Info */
.product-info {
    padding: 0;
    width: 100%;
    text-align: center;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.product-name {
    font-weight: 600;
    font-size: 0.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.3;
    margin-bottom: 0.375rem;
    color: #1f2937;
    transition: color 0.2s ease;
}

.product-item:hover .product-name {
    color: #3b82f6;
}

.product-price {
    color: #1e40af;
    font-weight: 700;
    font-size: 0.85rem;
    line-height: 1.3;
    margin-bottom: 0.375rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-price-currency {
    font-size: 0.7rem;
    margin-right: 0.125rem;
    opacity: 0.8;
}

.product-stock-container {
    margin-top: auto;
}

.product-stock {
    font-size: 0.65rem;
    color: #6b7280;
    line-height: 1.2;
    background-color: #f3f4f6;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    display: inline-block;
    transition: all 0.2s ease;
}

.product-stock.low {
    color: #fff;
    background-color: #ef4444;
}

.product-stock.medium {
    color: #fff;
    background-color: #f59e0b;
}

.product-stock.high {
    color: #fff;
    background-color: #10b981;
}

/* Category Navigation - Updated with bg-blue-900 styling */
.category-nav {
    padding: 1rem;
    border-bottom: 1px solid #1e3a8a;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative;
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.1);
}

.category-nav::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.category-nav::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 30px;
    background: linear-gradient(to right, rgba(30, 58, 138, 0), rgba(30, 58, 138, 1));
    pointer-events: none;
}

.category-filters {
    display: flex;
    gap: 0.75rem;
    padding-bottom: 0.25rem;
}

.category-filter {
    transition: all 0.25s ease;
    white-space: nowrap;
    border-radius: 0.75rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    min-height: 44px; /* Touch-friendly minimum height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-filter::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.25s ease;
}

.category-filter:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    color: white;
    background-color: rgba(255, 255, 255, 0.15);
}

.category-filter:hover::before {
    opacity: 1;
}

.category-filter.active {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
    color: white;
    border-color: #0ea5e9;
    box-shadow: 0 4px 8px rgba(14, 165, 233, 0.3);
    font-weight: 600;
    transform: translateY(-1px);
}

.category-filter.active::before {
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
}

.category-filter-icon {
    margin-right: 0.5rem;
    font-size: 0.875rem;
}

.category-filter-count {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.1);
    color: inherit;
    border-radius: 1rem;
    font-size: 0.7rem;
    padding: 0.125rem 0.375rem;
    margin-left: 0.5rem;
    min-width: 1.5rem;
}

.category-filter.active .category-filter-count {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Search Bar */
.search-container {
    position: relative;
    margin: 1rem;
}

.search-container input {
    padding: 0.875rem 1rem 0.875rem 3rem;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
    width: 100%;
    background-color: white;
    font-size: 0.95rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.search-container .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.search-container input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
    padding-left: 1.5rem;
}

.search-container input:focus + .search-icon {
    left: 0.5rem;
    color: #3b82f6;
}

.search-container .search-clear {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 0.875rem;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    background: none;
    border: none;
    padding: 0.25rem;
    border-radius: 50%;
}

.search-container input:not(:placeholder-shown) + .search-icon + .search-clear {
    opacity: 1;
}

.search-container .search-clear:hover {
    color: #6b7280;
    background-color: #f3f4f6;
}

/* Cart Styles - Enhanced with consistent blue theme */
.cart-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.03);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.cart-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08), 0 15px 20px rgba(0, 0, 0, 0.05);
    border-color: #3b82f6;
}

.cart-header {
    padding: 1.25rem;
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
    border-bottom: 1px solid #1e3a8a;
    position: relative;
    color: white;
}

.cart-header::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, transparent, #0ea5e9, transparent);
}

.cart-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.cart-title-icon {
    margin-right: 0.5rem;
    color: #93c5fd;
}

.cart-subtitle {
    font-size: 0.875rem;
    color: #cbd5e1;
}

.cart-body {
    padding: 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.cart-items {
    flex-grow: 1;
    overflow-y: auto;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
    max-height: 350px;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
    background-color: #f9fafb;
    margin-bottom: 1rem;
}

.cart-items::-webkit-scrollbar {
    width: 6px;
}

.cart-items::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.cart-items::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
}

.cart-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #9ca3af;
    text-align: center;
}

.cart-empty-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #d1d5db;
}

.cart-empty-text {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
}

.cart-empty-subtext {
    font-size: 0.8rem;
    color: #9ca3af;
}

.cart-item {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    position: relative;
    background-color: white;
}

.cart-item:hover {
    background-color: #f9fafb;
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.cart-item-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.cart-item-price {
    font-size: 0.85rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
}

.cart-item-price-currency {
    font-size: 0.7rem;
    margin-right: 0.125rem;
    opacity: 0.8;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    margin-top: 0.75rem;
}

.quantity-control {
    display: flex;
    align-items: center;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.quantity-btn {
    background-color: #f3f4f6;
    border: none;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4b5563;
}

.quantity-btn:hover {
    background-color: #e5e7eb;
    color: #1f2937;
}

.quantity-btn:active {
    transform: scale(0.95);
    background-color: #d1d5db;
}

.quantity-input {
    width: 45px;
    text-align: center;
    border: none;
    border-left: 1px solid #e5e7eb;
    border-right: 1px solid #e5e7eb;
    height: 32px;
    font-size: 0.875rem;
    color: #1f2937;
    font-weight: 500;
    background-color: white;
}

.quantity-input:focus {
    outline: none;
    background-color: #f9fafb;
}

.remove-btn {
    color: #ef4444;
    background: none;
    border: 1px solid transparent;
    cursor: pointer;
    font-size: 1.25rem;
    margin-left: 0.75rem;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
}

.remove-btn:hover {
    color: #b91c1c;
    background-color: #fee2e2;
    border-color: #fecaca;
}

.cart-item-total {
    text-align: right;
    font-weight: 600;
    margin-top: 0.75rem;
    color: #1e40af;
    font-size: 0.95rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.cart-item-total-currency {
    font-size: 0.8rem;
    margin-right: 0.125rem;
    opacity: 0.8;
}

.cart-summary {
    padding: 1rem;
    border-top: 1px solid #e5e7eb;
    background-color: #f9fafb;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
}

.cart-total-label {
    display: flex;
    align-items: center;
}

.cart-total-icon {
    margin-right: 0.5rem;
    color: #3b82f6;
}

.cart-total-value {
    color: #1e40af;
    display: flex;
    align-items: center;
}

.cart-total-currency {
    font-size: 0.9rem;
    margin-right: 0.125rem;
    opacity: 0.8;
}

.cart-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

@media (min-width: 640px) {
    .cart-actions {
        grid-template-columns: 1fr 1fr;
    }
}

/* Receipt Preview */
.receipt-preview {
    font-family: 'Courier New', monospace;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    max-width: 350px;
    margin-left: auto;
    margin-right: auto;
}

.receipt-preview:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px) rotate(1deg);
}

.receipt-preview::before {
    content: '';
    position: absolute;
    top: -5px;
    left: 15px;
    right: 15px;
    height: 5px;
    background: repeating-linear-gradient(
        90deg,
        #e5e7eb,
        #e5e7eb 5px,
        transparent 5px,
        transparent 10px
    );
}

.receipt-preview::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 15px;
    right: 15px;
    height: 5px;
    background: repeating-linear-gradient(
        90deg,
        #e5e7eb,
        #e5e7eb 5px,
        transparent 5px,
        transparent 10px
    );
}

.receipt-paper {
    position: relative;
    background-color: white;
    background-image:
        linear-gradient(rgba(222, 226, 230, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(222, 226, 230, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: -1px -1px;
}

.receipt-header {
    text-align: center;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px dashed #d1d5db;
}

.receipt-logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1e40af;
    margin-bottom: 0.5rem;
    letter-spacing: 1px;
}

.receipt-title {
    font-weight: bold;
    font-size: 1.1rem;
    color: #1f2937;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.receipt-subtitle {
    font-size: 0.8rem;
    color: #6b7280;
}

.receipt-info {
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #4b5563;
}

.receipt-info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.25rem;
}

.receipt-info-label {
    font-weight: 600;
}

.receipt-divider {
    border-top: 1px dashed #d1d5db;
    margin: 0.75rem 0;
}

.receipt-items-header {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e5e7eb;
}

.receipt-items {
    margin-bottom: 1rem;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    color: #4b5563;
}

.receipt-item-name {
    max-width: 70%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.receipt-item-price {
    font-weight: 500;
    text-align: right;
}

.receipt-item-qty {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.125rem;
}

.receipt-summary {
    border-top: 1px solid #e5e7eb;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
}

.receipt-summary-row {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 0.25rem;
    color: #4b5563;
}

.receipt-summary-label {
    font-weight: 500;
}

.receipt-total {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    margin-top: 0.5rem;
    font-size: 0.95rem;
    color: #1f2937;
    padding-top: 0.5rem;
    border-top: 1px dashed #d1d5db;
}

.receipt-footer {
    text-align: center;
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 1.5rem;
    padding-top: 0.75rem;
    border-top: 1px dashed #d1d5db;
}

.receipt-footer-message {
    margin-bottom: 0.5rem;
    font-style: italic;
}

.receipt-barcode {
    text-align: center;
    margin-top: 1rem;
    font-family: 'Libre Barcode 39', cursive;
    font-size: 2rem;
    letter-spacing: -1px;
    color: #1f2937;
}

.receipt-qr {
    display: block;
    width: 80px;
    height: 80px;
    margin: 1rem auto 0.5rem;
    background-color: #f3f4f6;
    padding: 0.5rem;
    border-radius: 0.25rem;
}

/* Print Button */
.print-button {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background-color: #1e40af;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
}

.print-button:hover {
    background-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(30, 64, 175, 0.3);
}

.print-button:active {
    transform: translateY(0);
}

.print-button-icon {
    margin-right: 0.5rem;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    border-radius: inherit;
    backdrop-filter: blur(2px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #e5e7eb;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner-sm {
    width: 24px;
    height: 24px;
    border-width: 2px;
}

.loading-spinner-lg {
    width: 60px;
    height: 60px;
    border-width: 4px;
}

.loading-text {
    margin-top: 1rem;
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
}

.btn-loading {
    position: relative;
    pointer-events: none;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 0.75s linear infinite;
}

/* Skeleton Loading */
.skeleton {
    background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 0.25rem;
}

.skeleton-text {
    height: 0.8rem;
    margin-bottom: 0.5rem;
    width: 100%;
}

.skeleton-text:last-child {
    width: 80%;
}

.skeleton-circle {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.skeleton-image {
    width: 100%;
    height: 0;
    padding-bottom: 100%;
    border-radius: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInRight {
    from { transform: translateX(20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
    from { transform: translateY(10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
    from { transform: translateY(-10px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.fade-in {
    animation: fadeIn 0.4s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.4s ease-in-out;
}

.slide-in {
    animation: slideInRight 0.4s ease-in-out;
}

.slide-in-left {
    animation: slideInLeft 0.4s ease-in-out;
}

.slide-up {
    animation: slideInUp 0.4s ease-in-out;
}

.slide-down {
    animation: slideInDown 0.4s ease-in-out;
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}

.spin {
    animation: spin 1s linear infinite;
}

.shimmer {
    background: linear-gradient(90deg, #f3f4f6 0%, #ffffff 50%, #f3f4f6 100%);
    background-size: 200% 100%;
    animation: shimmer 2s infinite linear;
}

.bounce {
    animation: bounce 1s ease infinite;
}

/* Transitions */
.transition-all {
    transition: all 0.3s ease;
}

.transition-transform {
    transition: transform 0.3s ease;
}

.transition-opacity {
    transition: opacity 0.3s ease;
}

.transition-colors {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Hover Effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-bright {
    transition: filter 0.3s ease;
}

.hover-bright:hover {
    filter: brightness(1.1);
}

/* Focus Styles */
.focus-ring {
    transition: box-shadow 0.2s ease;
}

.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 350px;
    width: calc(100% - 17rem); /* Account for sidebar width (16rem) plus margin */
    margin-right: 0;
}

.notification {
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: flex-start;
    overflow: hidden;
    animation: slideInRight 0.3s ease-out;
    position: relative;
    border-left: 4px solid #3b82f6;
}

.notification.closing {
    animation: slideOutRight 0.3s ease-in forwards;
}

.notification-icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    color: #3b82f6;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    color: #111827; /* Darker for better contrast on white background */
}

.notification-message {
    font-size: 0.85rem;
    color: #4b5563; /* text color of notification message */
}

.notification-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    transition: color 0.2s ease;
}

.notification-close:hover {
    color: #6b7280;
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: #3b82f6;
    width: 100%;
    transform-origin: left;
    animation: progress 5s linear forwards;
}

.notification.success {
    border-left-color: #10b981;
}

.notification.success .notification-icon {
    color: #10b981;
}

.notification.success .notification-title {
    color: #065f46; /* Darker green for title */
}

.notification.success .notification-progress {
    background-color: #10b981;
}

.notification.error {
    border-left-color: #ef4444;
}

.notification.error .notification-icon {
    color: #ef4444;
}

.notification.error .notification-title {
    color: #b91c1c; /* Darker red for title */
}

.notification.error .notification-progress {
    background-color: #ef4444;
}

.notification.warning {
    border-left-color: #f59e0b;
}

.notification.warning .notification-icon {
    color: #f59e0b;
}

.notification.warning .notification-progress {
    background-color: #f59e0b;
}

@keyframes progress {
    0% { transform: scaleX(1); }
    100% { transform: scaleX(0); }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Mobile Responsiveness */
@media (max-width: 640px) {
    .pos-page-container {
        padding: 0.5rem; /* Reduced mobile padding */
    }

    .pos-header {
        padding: 0.5rem 0.75rem; /* Reduced mobile header padding */
    }

    .pos-title {
        font-size: 1.25rem;
    }

    .pos-subtitle {
        font-size: 0.75rem;
    }

    .category-nav {
        padding: 0.5rem; /* Reduced mobile category nav padding */
    }

    .category-filter {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
    }

    .search-container {
        margin: 0.5rem; /* Reduced mobile search container margin */
    }

    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 0.5rem;
        padding: 0.5rem; /* Reduced mobile product grid padding */
        max-height: calc(100vh - 320px); /* Adjusted mobile height */
    }

    .product-image-container {
        width: 50px;
        height: 50px;
    }

    .product-name {
        font-size: 0.7rem;
    }

    .cart-header {
        padding: 0.75rem;
    }

    .cart-title {
        font-size: 1.1rem;
    }

    .cart-body {
        padding: 0.75rem;
    }

    .notification-container {
        max-width: 100%;
        width: calc(100% - 1rem);
        right: 0.5rem;
        top: 0.5rem;
    }
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 1rem;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.keyboard-shortcuts-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.keyboard-shortcuts-title i {
    margin-right: 0.5rem;
}

.keyboard-shortcuts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #4b5563;
}

.keyboard-shortcut-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.25rem;
}

.keyboard-shortcut-item span {
    margin-left: 0.5rem;
}

kbd {
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-family: monospace;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    color: #1f2937;
}

/* Form Elements */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: white;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.15);
}

.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: white;
    font-size: 0.95rem;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    min-height: 44px; /* Touch-friendly minimum height */
}

.form-select:focus {
    outline: none;
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.15);
    background-color: #fefefe;
}

.form-select:hover {
    border-color: #9ca3af;
    background-color: #fafafa;
}

/* Enhanced Payment Method Styling */
.payment-method-group {
    position: relative;
    margin-bottom: 1rem;
}

.payment-method-group .form-label {
    color: #1e40af;
    font-weight: 600;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
}

.payment-method-group .form-label::before {
    content: '💳';
    margin-right: 0.5rem;
    font-size: 1rem;
}

.payment-method-select {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1rem;
    font-weight: 500;
    color: #1e40af;
    box-shadow: 0 2px 4px rgba(30, 64, 175, 0.1);
}

.payment-method-select:focus {
    border-color: #1e40af;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.15), 0 4px 6px rgba(30, 64, 175, 0.1);
    background: linear-gradient(135deg, #eff6ff 0%, #ffffff 100%);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.25s ease;
    cursor: pointer;
    font-size: 0.95rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.25s ease;
}

.btn:hover::before {
    opacity: 1;
}

.btn-primary {
    background-color: #1e40af;
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
}

.btn-primary:hover {
    background-color: #1e3a8a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(30, 64, 175, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(30, 64, 175, 0.2);
}

.btn-success {
    background-color: #10b981;
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
    background-color: #059669;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-success:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
    background-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.btn-danger:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(239, 68, 68, 0.2);
}

.btn-warning {
    background-color: #f59e0b;
    color: white;
    border: none;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.btn-warning:hover {
    background-color: #d97706;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.btn-warning:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(245, 158, 11, 0.2);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #4b5563;
}

.btn-outline:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
    color: #1f2937;
    transform: translateY(-1px);
}

.btn-outline-primary {
    background-color: transparent;
    border: 1px solid #1e40af;
    color: #1e40af;
}

.btn-outline-primary:hover {
    background-color: #eff6ff;
    color: #1e3a8a;
    border-color: #1e3a8a;
    transform: translateY(-1px);
}

.btn-block {
    display: block;
    width: 100%;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    padding: 0;
}

.btn-icon-sm {
    width: 2rem;
    height: 2rem;
}

.btn-icon-lg {
    width: 3rem;
    height: 3rem;
    font-size: 1.25rem;
}

.btn-floating {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    background-color: #1e40af;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    z-index: 100;
}

.btn-floating:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

.btn-floating:active {
    transform: translateY(0) scale(0.95);
}

.btn-group {
    display: inline-flex;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.btn-group .btn {
    border-radius: 0;
    margin: 0;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    border-right: none;
}

/* Utilities */
.shadow-sm {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.03);
}

.shadow-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1), 0 20px 25px rgba(0, 0, 0, 0.05);
}

.rounded-lg {
    border-radius: 0.5rem;
}

.bg-white {
    background-color: white;
}

.bg-gray-50 {
    background-color: #f9fafb;
}

.text-primary {
    color: #1e40af;
}

.text-danger {
    color: #ef4444;
}

.text-success {
    color: #10b981;
}

.text-warning {
    color: #f59e0b;
}

.border-bottom {
    border-bottom: 1px solid #e5e7eb;
}

.p-4 {
    padding: 1rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

/* Quick Actions Section */
.pos-quick-actions {
    margin-top: 1rem;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.pos-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #1e40af;
    color: white;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.25s ease;
    box-shadow: 0 2px 4px rgba(30, 64, 175, 0.2);
    border: none;
    cursor: pointer;
}

.pos-action-btn:hover {
    background-color: #1e3a8a;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(30, 64, 175, 0.3);
}

.pos-action-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(30, 64, 175, 0.2);
}

.pos-action-btn:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.pos-action-btn i {
    font-size: 1rem;
}

/* Mobile Optimizations for Quick Actions */
@media (max-width: 640px) {
    .pos-quick-actions {
        margin-top: 0.75rem;
        padding: 0.5rem;
    }

    .pos-action-btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
}
